import { Canvas } from "fabric";
import { useCallback, useEffect, useRef } from "react";
import { UseResponsiveCanvasProps, CropData } from "@/shared/types";
import {
  calculateFittedCanvasDimensions,
  shouldResize,
  scaleCanvasObjects,
} from "@/lib/fabric/rendering";

const handleCroppedCanvasResize = (
  canvas: Canvas,
  container: HTMLElement,
  cropData: CropData,
  setCropData?: (data: CropData) => void
) => {
  if (!cropData?.normalizedCropRect) return;

  const containerRect = container.getBoundingClientRect();
  const currentCanvasWidth = canvas.getWidth();
  const currentCanvasHeight = canvas.getHeight();

  // For cropped images, we want to scale the cropped image to optimally fit the new container
  // Calculate the aspect ratio of the current cropped canvas
  const cropAspectRatio = currentCanvasWidth / currentCanvasHeight;
  const containerAspectRatio = containerRect.width / containerRect.height;

  // Calculate optimal dimensions to fit container while maintaining crop aspect ratio
  let targetWidth: number;
  let targetHeight: number;

  if (cropAspectRatio > containerAspectRatio) {
    // Crop is wider than container - fit to container width
    targetWidth = containerRect.width;
    targetHeight = containerRect.width / cropAspectRatio;
  } else {
    // Crop is taller than container - fit to container height
    targetHeight = containerRect.height;
    targetWidth = containerRect.height * cropAspectRatio;
  }

  if (!shouldResize(currentCanvasWidth, currentCanvasHeight, targetWidth, targetHeight)) {
    return;
  }

  // Calculate scale factors
  const scaleX = targetWidth / currentCanvasWidth;
  const scaleY = targetHeight / currentCanvasHeight;

  // Resize canvas to new dimensions
  canvas.setDimensions({ width: targetWidth, height: targetHeight });

  // Scale the background image to fill the new canvas size
  const backgroundImage = canvas.backgroundImage;
  if (backgroundImage) {
    backgroundImage.set({
      left: targetWidth / 2,
      top: targetHeight / 2,
      originX: "center",
      originY: "center",
      scaleX: (backgroundImage.scaleX || 1) * scaleX,
      scaleY: (backgroundImage.scaleY || 1) * scaleY,
    });
  }

  // Scale all objects (annotations) proportionally
  canvas.forEachObject((obj) => {
    if (obj === backgroundImage) return;

    obj.set({
      left: (obj.left || 0) * scaleX,
      top: (obj.top || 0) * scaleY,
      scaleX: (obj.scaleX || 1) * scaleX,
      scaleY: (obj.scaleY || 1) * scaleY,
    });
    obj.setCoords();
  });

  canvas.renderAll();

  if (setCropData) {
    const updatedCropData = {
      ...cropData,
      canvasDimensions: {
        width: containerRect.width,
        height: containerRect.height,
      },
    };
    setCropData(updatedCropData);
  }
};

const handleOriginalCanvasResize = (canvas: Canvas, container: HTMLElement) => {
  const containerRect = container.getBoundingClientRect();
  const currentWidth = canvas.getWidth();
  const currentHeight = canvas.getHeight();

  const { width: fittedWidth, height: fittedHeight } = calculateFittedCanvasDimensions(
    currentWidth,
    currentHeight,
    containerRect.width,
    containerRect.height
  );

  const { width: targetWidth, height: targetHeight } = {
    width: Math.max(fittedWidth, 300),
    height: Math.max(fittedHeight, 200),
  };
  if (!shouldResize(currentWidth, currentHeight, targetWidth, targetHeight)) {
    return;
  }

  const scaleX = targetWidth / currentWidth;
  const scaleY = targetHeight / currentHeight;
  const imageScale = Math.min(scaleX, scaleY);
  const actualWidth = currentWidth * imageScale;
  const actualHeight = currentHeight * imageScale;

  canvas.setDimensions({ width: actualWidth, height: actualHeight });
  canvas.renderOnAddRemove = false;

  scaleCanvasObjects(canvas, imageScale);

  const bgImg = canvas.backgroundImage;
  if (bgImg) {
    const currentAngle = bgImg.angle || 0;
    const currentFlipX = bgImg.flipX || false;
    const currentFlipY = bgImg.flipY || false;

    bgImg.scaleX = (bgImg.scaleX || 1) * imageScale;
    bgImg.scaleY = (bgImg.scaleY || 1) * imageScale;

    bgImg.set({
      left: actualWidth / 2,
      top: actualHeight / 2,
      originX: "center",
      originY: "center",
    });

    bgImg.angle = currentAngle;
    bgImg.flipX = currentFlipX;
    bgImg.flipY = currentFlipY;
  }

  canvas.renderOnAddRemove = true;
  canvas.requestRenderAll();
};

export const useResponsiveCanvas = ({
  fabricCanvas,
  containerRef,
  cropData,
  setCropData,
}: UseResponsiveCanvasProps) => {
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  const resizeCanvas = useCallback(() => {
    if (!fabricCanvas?.current || !containerRef.current) return;

    const canvas = fabricCanvas.current;

    if (cropData?.isCropped && cropData.normalizedCropRect && cropData.canvasDimensions) {
      handleCroppedCanvasResize(canvas, containerRef.current, cropData, setCropData);
    } else {
      handleOriginalCanvasResize(canvas, containerRef.current);
    }
  }, [fabricCanvas, containerRef, cropData, setCropData]);

  useEffect(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    resizeObserverRef.current = new ResizeObserver(() => {
      resizeCanvas();
    });

    resizeObserverRef.current.observe(container);

    return () => {
      resizeObserverRef.current?.disconnect();
      resizeObserverRef.current = null;
    };
  }, [containerRef, resizeCanvas]);

  return {
    resizeCanvas,
  };
};
