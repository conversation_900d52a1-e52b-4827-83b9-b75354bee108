import { useCallback, useState, useMemo } from "react";
import { Canvas } from "fabric";
import { applyCanvasFilters } from "@/lib/fabric/operations/filters";
import type { FilterState, FilterHandlers, FilterManagementState } from "@/shared/types";

export const useFilterManagement = (
  initialFilters: FilterState,
  fabricCanvas: React.RefObject<Canvas | null>
): FilterManagementState => {
  const [filters, setFilters] = useState<FilterState>(initialFilters);

  const updateFilter = useCallback((keyOrConfig: string | object, value?: number | boolean) => {
    if (typeof keyOrConfig === "object") {
      setFilters((prev) => ({ ...prev, ...keyOrConfig }));
    } else {
      setFilters((prev) => ({ ...prev, [keyOrConfig]: value }));
    }
  }, []);

  const filterHandlers = useMemo(() => {
    const createHandler = (key: string) => (value: number | boolean) => {
      updateFilter(key, value);
      if (fabricCanvas.current) applyCanvasFilters(fabricCanvas.current, { [key]: value });
    };

    return {
      setBrightness: createHandler("brightness"),
      setContrast: createHandler("contrast"),
      setGrayscale: createHandler("grayscale"),
      setInvert: createHandler("invert"),
      setSharpness: createHandler("sharpness"),
      setGammaR: createHandler("gammaR"),
      setGammaG: createHandler("gammaG"),
      setGammaB: createHandler("gammaB"),
    };
  }, [updateFilter, fabricCanvas]);

  return {
    filters,
    filterHandlers,
    updateFilter,
  };
};
