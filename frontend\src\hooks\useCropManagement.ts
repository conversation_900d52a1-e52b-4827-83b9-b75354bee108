import { useState } from "react";
import { Canvas } from "fabric";
import { CropData, CropManagementState, UndoTrackingState } from "@/shared/types";
import { handleCropOperation } from "@/lib/fabric/operations/crop";

export const useCropManagement = (
  fabricCanvas: React.RefObject<Canvas | null>,
  initialCropData: CropData,
  undoTracking: UndoTrackingState,
  containerRef?: React.RefObject<HTMLElement | null>,
  originalImageUrl?: string
): CropManagementState => {
  const [cropData, setCropData] = useState<CropData>(initialCropData);
  const [hasPerformedCrop, setHasPerformedCrop] = useState(initialCropData.isCropped || false);
  const [savedAnnotations, setSavedAnnotations] = useState<any>(null);

  const handleCrop = async () => {
    // Save annotations before cropping (for restore)
    if (!hasPerformedCrop && fabricCanvas.current) {
      const annotations = fabricCanvas.current
        .getObjects()
        .filter(
          (obj) => (obj as any).name !== "cropRect" && (obj as any).name !== "backgroundImage"
        );
      if (annotations.length > 0) {
        setSavedAnnotations({
          objects: annotations.map((obj) => obj.toObject(["name", "id"])),
          canvasWidth: fabricCanvas.current.getWidth(),
          canvasHeight: fabricCanvas.current.getHeight(),
        });
      }
    }

    const cropOperation = handleCropOperation(
      fabricCanvas,
      hasPerformedCrop,
      setCropData,
      undoTracking.isUndoingRef,
      setHasPerformedCrop,
      containerRef,
      originalImageUrl,
      savedAnnotations
    );

    await cropOperation();

    // Clear saved annotations after restore
    if (hasPerformedCrop) {
      setSavedAnnotations(null);
    }
  };

  return {
    cropData,
    setCropData,
    hasPerformedCrop,
    setHasPerformedCrop,
    handleCrop,
  };
};
