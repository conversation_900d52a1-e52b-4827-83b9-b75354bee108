import { Canvas } from "fabric";
import { CanvasConfig, FilterParams, SetupCanvasParams } from "@/shared/types";
import { loadAnnotations } from "../operations/annotations";
import { applyCanvasFilters } from "../operations/filters";
import { loadCanvasImage } from "../rendering/image";
import { createImageLoadContainer, applyCropToCanvas } from "../operations/crop";
import {
  applyCanvasRotation,
  applyCanvasFlipHorizontal,
  applyCanvasFlipVertical,
} from "../operations/transforms";

const DEFAULT_CANVAS_CONFIG: CanvasConfig = {
  selection: true,
  backgroundColor: "transparent",
} as const;

const canvasFilterStates = new Map<Canvas, FilterParams>();

// Creates and configures an image canvas with image, filters, and annotations
export const setupImageCanvas = async ({
  canvasElement,
  imageUrl,
  annotations,
  filters,
  cropData,
  existingCanvas,
  transformState,
}: SetupCanvasParams): Promise<{
  canvas: Canvas;
}> => {
  const finalImageSource = imageUrl;

  // Apply saved transforms
  const applyTransforms = () => {
    if (transformState?.rotations) {
      for (let i = 0; i < transformState.rotations; i++) {
        applyCanvasRotation(canvas);
      }
    }
    if (transformState?.flipHorizontal) {
      applyCanvasFlipHorizontal(canvas);
    }
    if (transformState?.flipVertical) {
      applyCanvasFlipVertical(canvas);
    }
  };
  if (existingCanvas) {
    existingCanvas.dispose();
  }

  const canvas = new Canvas(canvasElement, {
    ...DEFAULT_CANVAS_CONFIG,
  });

  const containerRect = canvasElement.parentElement?.getBoundingClientRect();

  if (cropData?.isCropped && cropData.normalizedCropRect) {
    const imageLoadContainer = createImageLoadContainer(cropData, containerRect);

    await loadCanvasImage(canvas, finalImageSource, {
      containerRect: imageLoadContainer || undefined,
    });
    applyTransforms();
    await applyCropToCanvas(canvas, cropData);
  } else {
    await loadCanvasImage(canvas, finalImageSource, {
      containerRect: containerRect || undefined,
    });
    applyTransforms();
  }

  // Apply filters if provided
  if (filters) {
    canvasFilterStates.set(canvas, { ...filters });
    canvas.renderAll();
    applyCanvasFilters(canvas, filters);
  }

  // Load annotations if provided
  if (annotations) {
    await loadAnnotations(canvas, annotations);
  }

  // Configure canvas for viewer mode (non-interactive annotations)
  canvas.selection = false;
  canvas.forEachObject((obj) => {
    const objName = (obj as unknown as Record<string, unknown>)?.name;
    if (objName !== "backgroundImage") {
      obj.selectable = false;
      obj.evented = false;
    }
  });

  return {
    canvas,
  };
};
