import { useState } from "react";
import { Canvas } from "fabric";
import { TransformState, ImageTransformsState } from "@/shared/types";
import {
  createRotateHandler,
  createFlipHorizontalHandler,
  createFlipVerticalHandler,
} from "@/lib/fabric/operations";

export const useImageTransforms = (
  fabricCanvas: React.RefObject<Canvas | null>,
  initialTransformState: TransformState = {
    rotations: 0,
    flipHorizontal: false,
    flipVertical: false,
  },
  onRotationComplete?: () => void
): ImageTransformsState => {
  const [transformState, setTransformState] = useState<TransformState>(initialTransformState);

  const handleRotate = createRotateHandler(fabricCanvas, setTransformState, onRotationComplete);
  const handleFlipHorizontal = createFlipHorizontalHandler(fabricCanvas, setTransformState);
  const handleFlipVertical = createFlipVerticalHandler(fabricCanvas, setTransformState);

  return {
    transformState,
    setTransformState,
    handleRotate,
    handleFlipHorizontal,
    handleFlipVertical,
  };
};
